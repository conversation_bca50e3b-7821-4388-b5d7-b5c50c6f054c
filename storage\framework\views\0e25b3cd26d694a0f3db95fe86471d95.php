<?php $__env->startSection('content'); ?>
<div class="info-card warning">
    <h2>🔐 Password Reset Request</h2>
    <p>We received a request to reset your password for your TTAJet account.</p>
</div>

<p>Dear <?php echo e($user->name); ?>,</p>

<p>You are receiving this email because we received a password reset request for your <strong>TTAJet Courier Service</strong> account.</p>

<div class="text-center" style="margin: 30px 0;">
    <a href="<?php echo e($resetUrl); ?>" class="btn btn-primary" style="font-size: 16px; padding: 15px 30px;">
        🔐 Reset Your Password
    </a>
</div>

<div class="info-card">
    <h3>⚠️ Important Security Information</h3>
    <ul style="margin: 15px 0; padding-left: 20px; line-height: 1.8;">
        <li>This password reset link will expire in <strong>60 minutes</strong></li>
        <li>If you did not request a password reset, please ignore this email</li>
        <li>Your password will remain unchanged if you don't click the reset link</li>
        <li>For security reasons, this link can only be used once</li>
    </ul>
</div>

<div class="info-card">
    <h3>📋 Account Details</h3>
    <table class="data-table">
        <tr>
            <td><strong>Account Email:</strong></td>
            <td><?php echo e($user->email); ?></td>
        </tr>
        <tr>
            <td><strong>Account Name:</strong></td>
            <td><?php echo e($user->name); ?></td>
        </tr>
        <tr>
            <td><strong>Request Time:</strong></td>
            <td><?php echo e(now()->format('F j, Y \a\t g:i A')); ?></td>
        </tr>
        <tr>
            <td><strong>IP Address:</strong></td>
            <td><?php echo e(request()->ip()); ?></td>
        </tr>
    </table>
</div>

<h3>What to Do Next:</h3>
<div style="margin: 20px 0;">
    <div style="display: flex; align-items: center; margin-bottom: 15px;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">1</div>
        <div>
            <strong>Click the Reset Button</strong><br>
            <span style="color: #6b7280;">Click the "Reset Your Password" button above to proceed</span>
        </div>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 15px;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">2</div>
        <div>
            <strong>Enter New Password</strong><br>
            <span style="color: #6b7280;">Choose a strong, unique password for your account</span>
        </div>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 15px;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">3</div>
        <div>
            <strong>Confirm Changes</strong><br>
            <span style="color: #6b7280;">Save your new password and log in to your account</span>
        </div>
    </div>
</div>

<div class="info-card">
    <h3>💡 Password Security Tips</h3>
    <ul style="margin: 15px 0; padding-left: 20px; line-height: 1.8;">
        <li>Use at least 8 characters with a mix of letters, numbers, and symbols</li>
        <li>Avoid using personal information like your name or birthdate</li>
        <li>Don't reuse passwords from other accounts</li>
        <li>Consider using a password manager for better security</li>
    </ul>
</div>

<div class="info-card error">
    <h3>🚨 Didn't Request This?</h3>
    <p>If you did not request a password reset, please take the following actions:</p>
    <ul style="margin: 15px 0; padding-left: 20px;">
        <li>Ignore this email - your password will remain unchanged</li>
        <li>Check your account for any suspicious activity</li>
        <li>Consider changing your password as a precaution</li>
        <li>Contact our support team if you have concerns</li>
    </ul>
</div>

<div style="background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin: 25px 0; text-align: center;">
    <p style="margin: 0; color: #6b7280; font-size: 14px;">
        <strong>Alternative Method:</strong><br>
        If the button above doesn't work, copy and paste this link into your browser:<br>
        <span style="font-family: monospace; background: #ffffff; padding: 5px; border-radius: 4px; word-break: break-all;"><?php echo e($resetUrl); ?></span>
    </p>
</div>

<div class="info-card">
    <h3>📞 Need Help?</h3>
    <p>If you're having trouble resetting your password or have any questions, please contact our support team:</p>
    <ul style="margin: 15px 0; padding-left: 20px;">
        <li>📞 Phone: <?php echo e(\App\Services\EmailService::getCompanyInfo()['phone']); ?></li>
        <li>📧 Email: <?php echo e(\App\Services\EmailService::getCompanyInfo()['email']); ?></li>
        <li>🌐 Website: <a href="<?php echo e(config('app.url')); ?>" style="color: #F97316;"><?php echo e(config('app.url')); ?></a></li>
    </ul>
</div>

<p>Thank you for using TTAJet Courier Service!</p>

<p>Best regards,<br>
<strong>The TTAJet Security Team</strong></p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layout', [
    'title' => 'Password Reset Request',
    'subtitle' => 'Reset your TTAJet account password'
], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/emails/password-reset.blade.php ENDPATH**/ ?>