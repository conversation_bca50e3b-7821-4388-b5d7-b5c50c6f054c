<?php $__env->startSection('content'); ?>
<div class="info-card success">
    <h2>✅ Booking Confirmed!</h2>
    <p>Your delivery request has been successfully received and is being processed.</p>
</div>

<p>Dear <?php echo e($customer->name); ?>,</p>

<p>Thank you for choosing <strong>TTAJet Courier Service</strong>! Your booking has been confirmed and our team is preparing to handle your delivery.</p>

<div class="info-card">
    <h3>📋 Booking Details</h3>
    <table class="data-table">
        <tr>
            <td><strong>Booking ID:</strong></td>
            <td><span style="font-family: monospace; background: #f3f4f6; padding: 2px 6px; border-radius: 4px;"><?php echo e($booking->booking_id); ?></span></td>
        </tr>
        <tr>
            <td><strong>Status:</strong></td>
            <td>
                <span style="background: #dbeafe; color: #1e40af; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">
                    <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                </span>
            </td>
        </tr>
        <tr>
            <td><strong>Package Type:</strong></td>
            <td><?php echo e(ucfirst($booking->package_type)); ?></td>
        </tr>
        <?php if($booking->package_weight): ?>
        <tr>
            <td><strong>Weight:</strong></td>
            <td><?php echo e($booking->package_weight); ?> kg</td>
        </tr>
        <?php endif; ?>
        <tr>
            <td><strong>Estimated Cost:</strong></td>
            <td><strong style="color: #F97316;"><?php echo e(\App\Services\EmailService::formatCurrency($booking->estimated_cost)); ?></strong></td>
        </tr>
        <tr>
            <td><strong>Payment Method:</strong></td>
            <td><?php echo e(ucfirst(str_replace('_', ' ', $booking->payment_method))); ?></td>
        </tr>
        <tr>
            <td><strong>Distance:</strong></td>
            <td><?php echo e(number_format($booking->distance_km, 1)); ?> km</td>
        </tr>
        <?php if($booking->estimated_duration_minutes): ?>
        <tr>
            <td><strong>Estimated Duration:</strong></td>
            <td><?php echo e($booking->estimated_duration_minutes); ?> minutes</td>
        </tr>
        <?php endif; ?>
    </table>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 25px 0;">
    <div class="info-card">
        <h3>📍 Pickup Details</h3>
        <p><strong>Address:</strong><br><?php echo e($booking->pickup_address); ?></p>
        <p><strong>Contact Person:</strong><br><?php echo e($booking->pickup_person_name); ?></p>
        <p><strong>Phone:</strong><br><?php echo e($booking->pickup_person_phone); ?></p>
        <?php if($booking->pickup_time_preference): ?>
        <p><strong>Preferred Time:</strong><br><?php echo e($booking->pickup_time_preference); ?></p>
        <?php endif; ?>
    </div>

    <div class="info-card">
        <h3>🎯 Delivery Details</h3>
        <p><strong>Address:</strong><br><?php echo e($booking->delivery_address); ?></p>
        <p><strong>Receiver:</strong><br><?php echo e($booking->receiver_name); ?></p>
        <p><strong>Phone:</strong><br><?php echo e($booking->receiver_phone); ?></p>
    </div>
</div>

<?php if($booking->package_description): ?>
<div class="info-card">
    <h3>📦 Package Description</h3>
    <p><?php echo e($booking->package_description); ?></p>
</div>
<?php endif; ?>

<?php if($booking->special_instructions): ?>
<div class="info-card warning">
    <h3>⚠️ Special Instructions</h3>
    <p><?php echo e($booking->special_instructions); ?></p>
</div>
<?php endif; ?>

<h3>What Happens Next?</h3>
<div style="margin: 20px 0;">
    <div style="display: flex; align-items: center; margin-bottom: 15px;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">1</div>
        <div>
            <strong>Confirmation & Processing</strong><br>
            <span style="color: #6b7280;">Your booking is confirmed and being processed by our team</span>
        </div>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 15px;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">2</div>
        <div>
            <strong>Rider Assignment</strong><br>
            <span style="color: #6b7280;">A rider will be assigned and will contact you for pickup</span>
        </div>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 15px;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">3</div>
        <div>
            <strong>Pickup & Delivery</strong><br>
            <span style="color: #6b7280;">Your package will be picked up and delivered to the destination</span>
        </div>
    </div>
    <div style="display: flex; align-items: center;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">4</div>
        <div>
            <strong>Completion</strong><br>
            <span style="color: #6b7280;">You'll receive confirmation once delivery is completed</span>
        </div>
    </div>
</div>

<div class="text-center" style="margin: 30px 0;">
    <a href="<?php echo e(\App\Services\EmailService::getTrackingUrl($booking->booking_id)); ?>" class="btn btn-primary">
        📍 Track Your Package
    </a>
    <a href="<?php echo e(\App\Services\EmailService::getBookingUrl($booking->id)); ?>" class="btn btn-secondary">
        📋 View Booking Details
    </a>
</div>

<div class="info-card">
    <h3>📞 Need Help?</h3>
    <p>If you have any questions or need to make changes to your booking, please contact us:</p>
    <ul style="margin: 15px 0; padding-left: 20px;">
        <li>📞 Phone: <?php echo e(\App\Services\EmailService::getCompanyInfo()['phone']); ?></li>
        <li>📧 Email: <?php echo e(\App\Services\EmailService::getCompanyInfo()['email']); ?></li>
        <li>💬 Reference your Booking ID: <strong><?php echo e($booking->booking_id); ?></strong></li>
    </ul>
</div>

<p>Thank you for choosing TTAJet Courier Service. We'll take great care of your package!</p>

<p>Best regards,<br>
<strong>The TTAJet Team</strong></p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layout', [
    'title' => 'Booking Confirmation',
    'subtitle' => 'Your delivery has been successfully booked'
], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/emails/booking-confirmation.blade.php ENDPATH**/ ?>