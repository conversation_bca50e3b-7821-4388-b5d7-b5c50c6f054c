<?php $__env->startSection('content'); ?>
<div class="info-card warning">
    <h2>🔔 New Booking Alert</h2>
    <p>A new delivery booking has been submitted and requires your attention.</p>
</div>

<p>Dear Admin,</p>

<p>A new booking has been received on the <strong>TTAJet Courier Service</strong> platform. Please review the details below and take appropriate action.</p>

<div class="info-card">
    <h3>📋 Booking Summary</h3>
    <table class="data-table">
        <tr>
            <td><strong>Booking ID:</strong></td>
            <td><span style="font-family: monospace; background: #f3f4f6; padding: 2px 6px; border-radius: 4px;"><?php echo e($booking->booking_id); ?></span></td>
        </tr>
        <tr>
            <td><strong>Customer:</strong></td>
            <td><?php echo e($customer->name); ?> (<?php echo e($customer->email); ?>)</td>
        </tr>
        <tr>
            <td><strong>Phone:</strong></td>
            <td><?php echo e($customer->phone_number ?? 'Not provided'); ?></td>
        </tr>
        <tr>
            <td><strong>Submitted:</strong></td>
            <td><?php echo e($booking->created_at->format('F j, Y \a\t g:i A')); ?></td>
        </tr>
        <tr>
            <td><strong>Status:</strong></td>
            <td>
                <span style="background: #fef3c7; color: #f59e0b; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 600;">
                    <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                </span>
            </td>
        </tr>
        <tr>
            <td><strong>Package Type:</strong></td>
            <td><?php echo e(ucfirst($booking->package_type)); ?></td>
        </tr>
        <?php if($booking->package_weight): ?>
        <tr>
            <td><strong>Weight:</strong></td>
            <td><?php echo e($booking->package_weight); ?> kg</td>
        </tr>
        <?php endif; ?>
        <tr>
            <td><strong>Estimated Cost:</strong></td>
            <td><strong style="color: #F97316;"><?php echo e(\App\Services\EmailService::formatCurrency($booking->estimated_cost)); ?></strong></td>
        </tr>
        <tr>
            <td><strong>Payment Method:</strong></td>
            <td><?php echo e(ucfirst(str_replace('_', ' ', $booking->payment_method))); ?></td>
        </tr>
        <tr>
            <td><strong>Distance:</strong></td>
            <td><?php echo e(number_format($booking->distance_km, 1)); ?> km</td>
        </tr>
    </table>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 25px 0;">
    <div class="info-card">
        <h3>📍 Pickup Information</h3>
        <p><strong>Address:</strong><br><?php echo e($booking->pickup_address); ?></p>
        <p><strong>Contact Person:</strong><br><?php echo e($booking->pickup_person_name); ?></p>
        <p><strong>Phone:</strong><br><?php echo e($booking->pickup_person_phone); ?></p>
        <?php if($booking->pickup_time_preference): ?>
        <p><strong>Preferred Time:</strong><br><?php echo e($booking->pickup_time_preference); ?></p>
        <?php endif; ?>
    </div>

    <div class="info-card">
        <h3>🎯 Delivery Information</h3>
        <p><strong>Address:</strong><br><?php echo e($booking->delivery_address); ?></p>
        <p><strong>Receiver:</strong><br><?php echo e($booking->receiver_name); ?></p>
        <p><strong>Phone:</strong><br><?php echo e($booking->receiver_phone); ?></p>
    </div>
</div>

<?php if($booking->package_description): ?>
<div class="info-card">
    <h3>📦 Package Description</h3>
    <p><?php echo e($booking->package_description); ?></p>
</div>
<?php endif; ?>

<?php if($booking->special_instructions): ?>
<div class="info-card warning">
    <h3>⚠️ Special Instructions</h3>
    <p><?php echo e($booking->special_instructions); ?></p>
</div>
<?php endif; ?>

<div class="info-card">
    <h3>👤 Customer Information</h3>
    <table class="data-table">
        <tr>
            <td><strong>Name:</strong></td>
            <td><?php echo e($customer->name); ?></td>
        </tr>
        <tr>
            <td><strong>Email:</strong></td>
            <td><?php echo e($customer->email); ?></td>
        </tr>
        <tr>
            <td><strong>Phone:</strong></td>
            <td><?php echo e($customer->phone_number ?? 'Not provided'); ?></td>
        </tr>
        <tr>
            <td><strong>Member Since:</strong></td>
            <td><?php echo e($customer->created_at->format('F j, Y')); ?></td>
        </tr>
        <tr>
            <td><strong>Total Bookings:</strong></td>
            <td><?php echo e($customer->bookings()->count()); ?> bookings</td>
        </tr>
    </table>
</div>

<h3>Required Actions:</h3>
<div style="margin: 20px 0;">
    <div style="display: flex; align-items: center; margin-bottom: 15px;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">1</div>
        <div>
            <strong>Review Booking Details</strong><br>
            <span style="color: #6b7280;">Verify all information is correct and complete</span>
        </div>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 15px;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">2</div>
        <div>
            <strong>Confirm or Modify</strong><br>
            <span style="color: #6b7280;">Confirm the booking or make necessary adjustments</span>
        </div>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 15px;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">3</div>
        <div>
            <strong>Assign Rider</strong><br>
            <span style="color: #6b7280;">Find and assign an available rider for the delivery</span>
        </div>
    </div>
    <div style="display: flex; align-items: center;">
        <div style="width: 30px; height: 30px; background: #F97316; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; font-weight: bold;">4</div>
        <div>
            <strong>Monitor Progress</strong><br>
            <span style="color: #6b7280;">Track the delivery progress and handle any issues</span>
        </div>
    </div>
</div>

<div class="text-center" style="margin: 30px 0;">
    <a href="<?php echo e(route('admin.bookings.show', $booking->id)); ?>" class="btn btn-primary">
        📋 View Full Booking Details
    </a>
    <a href="<?php echo e(route('admin.bookings.index')); ?>" class="btn btn-secondary">
        📊 Go to Bookings Dashboard
    </a>
</div>

<?php if($booking->payment_method === 'cash_on_delivery'): ?>
<div class="info-card warning">
    <h3>💰 Payment Notice</h3>
    <p>This is a <strong>Cash on Delivery</strong> booking. Please ensure the rider collects the payment upon delivery.</p>
    <p><strong>Amount to Collect:</strong> <?php echo e(\App\Services\EmailService::formatCurrency($booking->estimated_cost)); ?></p>
</div>
<?php endif; ?>

<div class="info-card">
    <h3>📊 Quick Stats</h3>
    <p>Here are some quick statistics for today:</p>
    <ul style="margin: 15px 0; padding-left: 20px;">
        <li><strong>Total Bookings Today:</strong> <?php echo e(\App\Models\Booking::whereDate('created_at', today())->count()); ?></li>
        <li><strong>Pending Bookings:</strong> <?php echo e(\App\Models\Booking::where('status', 'pending')->count()); ?></li>
        <li><strong>Active Deliveries:</strong> <?php echo e(\App\Models\Booking::whereIn('status', ['confirmed', 'assigned', 'picked_up', 'in_transit'])->count()); ?></li>
    </ul>
</div>

<p>Please log into the admin dashboard to manage this booking and assign a rider.</p>

<p>Best regards,<br>
<strong>TTAJet System</strong></p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layout', [
    'title' => 'New Booking Alert',
    'subtitle' => 'A new delivery booking has been received'
], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/emails/admin-new-booking.blade.php ENDPATH**/ ?>