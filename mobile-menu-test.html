<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Menu Test - TTAJet</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        /* Mobile Menu Styles */
        .mobile-menu-button {
            display: block;
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.2s ease-in-out;
            z-index: 60;
            position: relative;
        }

        .mobile-menu-button:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .mobile-menu-button:focus {
            outline: 2px solid #F97316;
            outline-offset: 2px;
        }

        /* Ensure Alpine.js x-show works properly */
        [x-cloak] {
            display: none !important;
        }

        /* Mobile menu visibility states */
        .mobile-menu-hidden {
            display: none;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.2s ease-in-out;
        }

        .mobile-menu-visible {
            display: block;
            opacity: 1;
            transform: translateY(0);
            transition: all 0.2s ease-in-out;
        }

        /* Ensure mobile menu button is visible on all mobile devices */
        @media (max-width: 767px) {
            .md\:hidden {
                display: block !important;
            }
            
            .mobile-menu-button {
                min-width: 44px;
                min-height: 44px;
                display: flex !important;
                align-items: center;
                justify-content: center;
            }
            
            /* Force mobile menu to be properly positioned */
            header [x-show="mobileMenuOpen"] {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                width: 100%;
                z-index: 999;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    
    <header class="bg-black text-white absolute top-0 w-full z-50 backdrop-blur-sm" x-data="{ mobileMenuOpen: false }" x-cloak>
        <nav class="container mx-auto px-6 py-4 flex justify-between items-center">
            <!-- Logo -->
            <a href="#" class="text-2xl font-bold cursor-pointer">
                TTAJET
            </a>
            
            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="#" class="text-gray-300 hover:text-white transition-colors">About</a>
                <a href="#" class="text-gray-300 hover:text-white transition-colors">Services</a>
                <a href="#" class="text-gray-300 hover:text-white transition-colors">Book a Delivery</a>
                <a href="#" class="text-gray-300 hover:text-white transition-colors">Track Booking</a>
                <a href="#" class="text-gray-300 hover:text-white transition-colors">Login</a>
                <a href="#" class="bg-orange-600 px-5 py-2.5 rounded-lg text-sm font-semibold hover:bg-orange-700 transition-colors">Sign Up</a>
            </div>
            
            <!-- Mobile Menu Button -->
            <div class="md:hidden">
                <button @click="mobileMenuOpen = !mobileMenuOpen; console.log('Mobile menu toggled:', mobileMenuOpen)" 
                        class="mobile-menu-button text-white focus:outline-none"
                        aria-label="Toggle mobile menu"
                        :aria-expanded="mobileMenuOpen"
                        type="button">
                    <i class="fas text-2xl" :class="{ 'fa-bars': !mobileMenuOpen, 'fa-times': mobileMenuOpen }"></i>
                </button>
            </div>
        </nav>
        
        <!-- Mobile Navigation Menu -->
        <div x-show="mobileMenuOpen" 
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform -translate-y-1"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-1"
             @click.away="mobileMenuOpen = false"
             class="md:hidden bg-black/95 backdrop-blur-sm border-t border-gray-800 w-full"
             role="navigation"
             aria-label="Mobile navigation menu"
             style="display: none;"
             x-cloak>
            
            <div class="px-6 py-4 space-y-4">
                <a href="#" class="block hover:text-orange-500 transition-colors">About</a>
                <a href="#" class="block hover:text-orange-500 transition-colors">Services</a>
                <a href="#" class="block hover:text-orange-500 transition-colors">Track Package</a>
                <a href="#" class="block hover:text-orange-500 transition-colors">Book a Delivery</a>
                <a href="#" class="block hover:text-orange-500 transition-colors">Login</a>
                <a href="#" class="block bg-orange-500 px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors text-center">Sign Up</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-24 min-h-screen">
        <div class="container mx-auto px-6 py-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Mobile Menu Test</h1>
            <p class="text-gray-600 mb-4">This page tests the mobile menu functionality. Resize your browser window to mobile size (below 768px) to see the mobile menu button.</p>
            
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-semibold mb-4">Test Instructions:</h2>
                <ol class="list-decimal list-inside space-y-2 text-gray-700">
                    <li>Resize your browser window to mobile size (width less than 768px)</li>
                    <li>You should see a hamburger menu icon (☰) in the top right corner</li>
                    <li>Click the hamburger icon to open the mobile menu</li>
                    <li>The icon should change to an X (✕) and the menu should slide down</li>
                    <li>Click the X icon or click outside the menu to close it</li>
                    <li>The menu should slide up and the icon should change back to hamburger</li>
                </ol>
                
                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h3 class="font-semibold text-blue-900">Debug Information:</h3>
                    <p class="text-blue-800">Check the browser console for debug messages when clicking the mobile menu button.</p>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Enhanced mobile menu functionality with Alpine.js support and fallback
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Mobile menu script loaded');
            
            // Wait for Alpine.js to initialize
            setTimeout(function() {
                const mobileButton = document.querySelector('.mobile-menu-button');
                const mobileMenu = document.querySelector('[x-show="mobileMenuOpen"]');
                
                if (mobileButton && mobileMenu) {
                    console.log('Mobile menu elements found');
                    
                    // Ensure initial state
                    mobileMenu.style.display = 'none';
                    
                    // Enhanced click handler (fallback)
                    mobileButton.addEventListener('click', function(e) {
                        console.log('Mobile menu button clicked (fallback handler)');
                        
                        const isHidden = mobileMenu.style.display === 'none';
                        
                        if (isHidden) {
                            // Show menu
                            mobileMenu.style.display = 'block';
                            mobileButton.querySelector('i').className = 'fas fa-times text-2xl';
                            mobileButton.setAttribute('aria-expanded', 'true');
                        } else {
                            // Hide menu
                            mobileMenu.style.display = 'none';
                            mobileButton.querySelector('i').className = 'fas fa-bars text-2xl';
                            mobileButton.setAttribute('aria-expanded', 'false');
                        }
                    });
                    
                } else {
                    console.log('Mobile menu elements not found');
                }
            }, 100);
        });
    </script>

</body>
</html>
