<?php $__env->startSection('title', 'TTAJet - Fast and Reliable Courier Services'); ?>

<?php $__env->startSection('content'); ?>

<?php
    // Get settings for pricing display
    $pricingSettings = \App\Models\Setting::getGroup('pricing');

    // Set default pricing if settings don't exist
    $baseCosts = [
        'document' => $pricingSettings['base_cost_document'] ?? 15.00,
        'small' => $pricingSettings['base_cost_small'] ?? 20.00,
        'medium' => $pricingSettings['base_cost_medium'] ?? 35.00,
        'large' => $pricingSettings['base_cost_large'] ?? 50.00,
    ];
?>

<div id="app">

    <!-- Hero Section -->
    <?php if (isset($component)) { $__componentOriginala038281ce129721dd88a49670137597b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala038281ce129721dd88a49670137597b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.hero-section','data' => ['title' => 'Fast & Reliable Courier Services','subtitle' => 'Book a courier with TTA Jet for quick, secure, and professional delivery service across Accra.','primaryButtonText' => 'Join Now','primaryButtonUrl' => ''.e(route('register')).'','secondaryButtonText' => 'About Us','secondaryButtonUrl' => '#about-preview']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('hero-section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Fast & Reliable Courier Services','subtitle' => 'Book a courier with TTA Jet for quick, secure, and professional delivery service across Accra.','primary-button-text' => 'Join Now','primary-button-url' => ''.e(route('register')).'','secondary-button-text' => 'About Us','secondary-button-url' => '#about-preview']); ?>

         <?php $__env->slot('form', null, []); ?> 
            <?php if(auth()->guard()->guest()): ?>
                <?php if (isset($component)) { $__componentOriginal50027a91f1d993598d1cec741300a0d0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal50027a91f1d993598d1cec741300a0d0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.quick-booking-form','data' => ['action' => ''.e(route('booking.create')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('quick-booking-form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => ''.e(route('booking.create')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal50027a91f1d993598d1cec741300a0d0)): ?>
<?php $attributes = $__attributesOriginal50027a91f1d993598d1cec741300a0d0; ?>
<?php unset($__attributesOriginal50027a91f1d993598d1cec741300a0d0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal50027a91f1d993598d1cec741300a0d0)): ?>
<?php $component = $__componentOriginal50027a91f1d993598d1cec741300a0d0; ?>
<?php unset($__componentOriginal50027a91f1d993598d1cec741300a0d0); ?>
<?php endif; ?>
            <?php else: ?>
                <?php if (isset($component)) { $__componentOriginalf225215015f2c1140bc03ba841300625 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf225215015f2c1140bc03ba841300625 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glass-card','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glass-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <h2 class="text-2xl font-bold mb-6">Welcome back!</h2>
                    <div class="text-center"> 
                        <p class="text-gray-300 mb-4">Hello, <?php echo e(auth()->user()->name); ?>!</p>
                        <a href="<?php echo e(route('booking.create')); ?>"
                           class="inline-block w-full bg-orange-600 text-white font-bold py-3 rounded-lg hover:bg-orange-700 transition-colors text-center mb-3">
                            Create New Booking
                        </a>
                        <a href="<?php echo e(route('customer.dashboard')); ?>"
                           class="inline-block w-full bg-white/10 border border-white/20 text-white font-semibold py-3 rounded-lg hover:bg-white/20 transition-colors text-center">
                            View Dashboard
                        </a>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf225215015f2c1140bc03ba841300625)): ?>
<?php $attributes = $__attributesOriginalf225215015f2c1140bc03ba841300625; ?>
<?php unset($__attributesOriginalf225215015f2c1140bc03ba841300625); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf225215015f2c1140bc03ba841300625)): ?>
<?php $component = $__componentOriginalf225215015f2c1140bc03ba841300625; ?>
<?php unset($__componentOriginalf225215015f2c1140bc03ba841300625); ?>
<?php endif; ?>
            <?php endif; ?>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala038281ce129721dd88a49670137597b)): ?>
<?php $attributes = $__attributesOriginala038281ce129721dd88a49670137597b; ?>
<?php unset($__attributesOriginala038281ce129721dd88a49670137597b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala038281ce129721dd88a49670137597b)): ?>
<?php $component = $__componentOriginala038281ce129721dd88a49670137597b; ?>
<?php unset($__componentOriginala038281ce129721dd88a49670137597b); ?>
<?php endif; ?>

    <!-- About Preview Section -->
    <section id="about-preview" class="py-20 md:py-28 bg-white" style="margin-top: 10vh;">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900">Your Trusted Partner in Delivery</h2>
                <p class="mt-4 text-lg text-gray-600">TTA Jet is your trusted partner for fast and reliable courier delivery within Accra. We're committed to providing exceptional service with every package we handle. Our mission is to bridge distances, connecting people and businesses with efficiency and care.</p>
                <a href="#about" class="mt-8 inline-block font-semibold brand-orange-text hover:underline text-lg">Learn More About Us <i class="fas fa-arrow-right ml-1 text-sm"></i></a>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="" style="margin-bottom: 20vh;">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl md:text-4xl font-bold mb-16 text-center text-gray-900">Our Core Services</h2>
            <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php if (isset($component)) { $__componentOriginale804957ecdb153e8c822de5ed47a4ace = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale804957ecdb153e8c822de5ed47a4ace = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.service-card','data' => ['icon' => 'fas fa-shipping-fast','title' => 'Same-Day Delivery','description' => 'Get your packages delivered on the same day with unparalleled speed and care.']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('service-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'fas fa-shipping-fast','title' => 'Same-Day Delivery','description' => 'Get your packages delivered on the same day with unparalleled speed and care.']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale804957ecdb153e8c822de5ed47a4ace)): ?>
<?php $attributes = $__attributesOriginale804957ecdb153e8c822de5ed47a4ace; ?>
<?php unset($__attributesOriginale804957ecdb153e8c822de5ed47a4ace); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale804957ecdb153e8c822de5ed47a4ace)): ?>
<?php $component = $__componentOriginale804957ecdb153e8c822de5ed47a4ace; ?>
<?php unset($__componentOriginale804957ecdb153e8c822de5ed47a4ace); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginale804957ecdb153e8c822de5ed47a4ace = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale804957ecdb153e8c822de5ed47a4ace = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.service-card','data' => ['icon' => 'fas fa-box-open','title' => 'Small Packages','description' => 'We handle all your small parcels with maximum efficiency and security.']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('service-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'fas fa-box-open','title' => 'Small Packages','description' => 'We handle all your small parcels with maximum efficiency and security.']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale804957ecdb153e8c822de5ed47a4ace)): ?>
<?php $attributes = $__attributesOriginale804957ecdb153e8c822de5ed47a4ace; ?>
<?php unset($__attributesOriginale804957ecdb153e8c822de5ed47a4ace); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale804957ecdb153e8c822de5ed47a4ace)): ?>
<?php $component = $__componentOriginale804957ecdb153e8c822de5ed47a4ace; ?>
<?php unset($__componentOriginale804957ecdb153e8c822de5ed47a4ace); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginale804957ecdb153e8c822de5ed47a4ace = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale804957ecdb153e8c822de5ed47a4ace = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.service-card','data' => ['icon' => 'fas fa-store','title' => 'Business Solutions','description' => 'Tailored courier services to meet the demands of your small business.']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('service-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'fas fa-store','title' => 'Business Solutions','description' => 'Tailored courier services to meet the demands of your small business.']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale804957ecdb153e8c822de5ed47a4ace)): ?>
<?php $attributes = $__attributesOriginale804957ecdb153e8c822de5ed47a4ace; ?>
<?php unset($__attributesOriginale804957ecdb153e8c822de5ed47a4ace); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale804957ecdb153e8c822de5ed47a4ace)): ?>
<?php $component = $__componentOriginale804957ecdb153e8c822de5ed47a4ace; ?>
<?php unset($__componentOriginale804957ecdb153e8c822de5ed47a4ace); ?>
<?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 md:py-28 flex items-center justify-center h-screen">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl md:text-4xl font-bold mb-16 text-center text-white">Simple, Transparent Pricing</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <?php if (isset($component)) { $__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.price-card','data' => ['icon' => 'fas fa-file-alt','title' => 'Document','weight' => '< 0.5 kg','price' => ''.e(\App\Models\Setting::formatCurrency($baseCosts['document'])).'','bookingUrl' => ''.e(route('booking.create')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('price-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'fas fa-file-alt','title' => 'Document','weight' => '< 0.5 kg','price' => ''.e(\App\Models\Setting::formatCurrency($baseCosts['document'])).'','booking-url' => ''.e(route('booking.create')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61)): ?>
<?php $attributes = $__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61; ?>
<?php unset($__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61)): ?>
<?php $component = $__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61; ?>
<?php unset($__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.price-card','data' => ['icon' => 'fas fa-box','title' => 'Small Box','weight' => '0.5 - 2 kg','price' => ''.e(\App\Models\Setting::formatCurrency($baseCosts['small'])).'','popular' => true,'bookingUrl' => ''.e(route('booking.create')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('price-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'fas fa-box','title' => 'Small Box','weight' => '0.5 - 2 kg','price' => ''.e(\App\Models\Setting::formatCurrency($baseCosts['small'])).'','popular' => true,'booking-url' => ''.e(route('booking.create')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61)): ?>
<?php $attributes = $__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61; ?>
<?php unset($__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61)): ?>
<?php $component = $__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61; ?>
<?php unset($__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.price-card','data' => ['icon' => 'fas fa-box-open','title' => 'Medium Box','weight' => '2 - 5 kg','price' => ''.e(\App\Models\Setting::formatCurrency($baseCosts['medium'])).'','bookingUrl' => ''.e(route('booking.create')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('price-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'fas fa-box-open','title' => 'Medium Box','weight' => '2 - 5 kg','price' => ''.e(\App\Models\Setting::formatCurrency($baseCosts['medium'])).'','booking-url' => ''.e(route('booking.create')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61)): ?>
<?php $attributes = $__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61; ?>
<?php unset($__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61)): ?>
<?php $component = $__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61; ?>
<?php unset($__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.price-card','data' => ['icon' => 'fas fa-boxes','title' => 'Large Box','weight' => '> 5 kg','price' => ''.e(\App\Models\Setting::formatCurrency($baseCosts['large'])).'','bookingUrl' => ''.e(route('booking.create')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('price-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'fas fa-boxes','title' => 'Large Box','weight' => '> 5 kg','price' => ''.e(\App\Models\Setting::formatCurrency($baseCosts['large'])).'','booking-url' => ''.e(route('booking.create')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61)): ?>
<?php $attributes = $__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61; ?>
<?php unset($__attributesOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61)): ?>
<?php $component = $__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61; ?>
<?php unset($__componentOriginal5aa8ed93172a4a6b3c5e8f7e14c11e61); ?>
<?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Reviews Section -->
    <section id="reviews" class="bg-white py-20 md:py-28">
        <div class="container mx-auto px-6" style="margin: 10vh auto;">
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <div class="review-text-content">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900">Discover What Our Clients Say About Us!</h2>
                    <p class="mt-4 text-lg text-gray-600">Our platform involves a comprehensive approach focused on attracting new customers while ensuring the loyalty and satisfaction of existing ones.</p>
                </div>
                <div class="relative items-center justify-center">
                    <div class="flex items-center justify-center">
                        <div id="review-card" class="bg-white p-8 rounded-2xl shadow-2xl border absolute w-full max-w-[80%]">
                            <p id="reviewer-quote" class="text-lg text-gray-700">"As a busy professional, managing shipments can be challenging, but TTAJet simplifies this task by providing me with the tools to stay on top of my logistics."</p>
                            <div class="mt-4 border-t pt-4">
                                <p id="reviewer-name" class="font-bold text-gray-900">Ama Serwaa</p>
                                <p id="reviewer-role" class="text-sm text-gray-500">CEO, Serwaa Enterprises</p>
                            </div>
                        </div>
                    </div>
                    <button id="prev-review" class="absolute top-1/2 -translate-y-1/2 -left-6 w-12 h-12 bg-orange-600 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-orange-700 transition-colors">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button id="next-review" class="absolute top-1/2 -translate-y-1/2 -right-6 w-12 h-12 bg-orange-600 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-orange-700 transition-colors">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Additional page-specific JavaScript can be added here
    // The main animations and functionality are handled by the unified app.js
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/welcome.blade.php ENDPATH**/ ?>